-- ====================================================================
-- MISSING TABLES PATCH - FIX FOR CASHSHOPMANAGER ERRORS
-- ====================================================================
-- This patch adds the missing tables that cause CashShopManager errors:
-- - shop_purchases
-- - shop_removals
--
-- Run this ONLY if you already imported the previous database
-- and are getting "Table doesn't exist" errors for these tables.
-- ====================================================================

USE `not-aion`;

-- ----------------------------
-- `shop_purchases` (Missing table for CashShopManager)
-- ----------------------------

CREATE TABLE IF NOT EXISTS `shop_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL,
  `player_id` int(11) DEFAULT NULL,
  `item_id` int(11) NOT NULL,
  `item_count` int(11) NOT NULL DEFAULT '1',
  `price` int(11) NOT NULL,
  `purchase_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('PENDING','COMPLETED','FAILED') NOT NULL DEFAULT 'PENDING',
  `processed` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `account_id` (`account_id`),
  KEY `player_id` (`player_id`),
  KEY `status` (`status`),
  KEY `processed` (`processed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `shop_removals` (Missing table for CashShopManager)
-- ----------------------------

CREATE TABLE IF NOT EXISTS `shop_removals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL,
  `player_id` int(11) DEFAULT NULL,
  `item_id` int(11) NOT NULL,
  `item_count` int(11) NOT NULL DEFAULT '1',
  `removal_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reason` varchar(255) DEFAULT NULL,
  `status` enum('PENDING','COMPLETED','FAILED') NOT NULL DEFAULT 'PENDING',
  `processed` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `account_id` (`account_id`),
  KEY `player_id` (`player_id`),
  KEY `status` (`status`),
  KEY `processed` (`processed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ====================================================================
-- PATCH COMPLETE
-- ====================================================================
-- The missing tables have been added!
-- Your CashShopManager errors should now be resolved.
-- 
-- Note: Change the database name in the USE statement above 
-- if your database is named differently than 'not-aion'
-- ====================================================================
