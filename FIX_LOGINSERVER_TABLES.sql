-- ====================================================================
-- FIX LOGINSERVER TABLES - ADD MISSING TABLES AND COLUMNS
-- ====================================================================
-- This script fixes the LoginServer database issues:
-- 1. Creates missing access_log table
-- 2. Adds missing penalty_reason column to account_time table
-- ====================================================================

-- Change to your database name
USE `not-aion`;

-- Fix 1: Create missing access_log table
CREATE TABLE IF NOT EXISTS `access_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_name` varchar(45) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `response` int(11) NOT NULL,
  `ip` varchar(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ip` (`ip`),
  KEY `date` (`date`),
  KEY `response` (`response`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Fix 2: Add missing penalty_reason column to account_time table
ALTER TABLE `account_time` 
ADD COLUMN `penalty_reason` varchar(255) DEFAULT NULL AFTER `penalty_end`;

-- Verify the fixes
SELECT 'access_log table created successfully' AS status;
DESCRIBE access_log;

SELECT 'account_time table updated successfully' AS status;
DESCRIBE account_time;

-- ====================================================================
-- LOGINSERVER FIXES COMPLETE!
-- ====================================================================
-- The following fixes have been applied:
--
-- 1. access_log table created with columns:
--    - id (auto increment primary key)
--    - account_name (varchar(45))
--    - date (timestamp, default CURRENT_TIMESTAMP)
--    - response (int(11) - login response code)
--    - ip (varchar(20) - client IP address)
--
-- 2. account_time table updated with:
--    - penalty_reason (varchar(255)) column added
--
-- Your LoginServer errors should now be resolved!
-- Players should be able to login without database errors.
-- ====================================================================
