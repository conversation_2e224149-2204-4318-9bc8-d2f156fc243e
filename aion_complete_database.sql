-- ====================================================================
-- AION COMPLETE DATABASE - SINGLE FILE IMPORT
-- ====================================================================
-- This file combines both LoginServer and GameServer databases
-- Import this into a single database (e.g., 'aionx_complete')
-- ====================================================================

SET FOREIGN_KEY_CHECKS = 0;

-- ====================================================================
-- LOGINSERVER TABLES (Account Management)
-- ====================================================================

-- ----------------------------
-- account_data
-- ----------------------------

DROP TABLE IF EXISTS `account_data`;
CREATE TABLE `account_data` (
  `id` int(11) NOT NULL auto_increment,
  `name` varchar(45) NOT NULL,
  `password` varchar(65) NOT NULL,
  `activated` boolean NOT NULL DEFAULT TRUE, 
  `access_level` tinyint(3) NOT NULL default '0',
  `membership` tinyint(3) NOT NULL default '0',
  `last_server` tinyint(3) NOT NULL default '-1',
  `last_ip` varchar(20) default NULL,
  `ip_force` varchar(20) default NULL,
  `expire` date default NULL,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- account_time
-- ----------------------------

DROP TABLE IF EXISTS `account_time`;
CREATE TABLE `account_time` (
  `account_id` int(11) NOT NULL,
  `last_active` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
  `expiration_time` timestamp NULL default NULL,
  `session_duration` int(10) default '0',
  `accumulated_online` int(10) default '0',
  `accumulated_rest` int(10) default '0',
  `penalty_end` timestamp NULL default NULL,
  `penalty_reason` varchar(255) default NULL,
  PRIMARY KEY  (`account_id`),
  FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- banned_ip
-- ----------------------------

DROP TABLE IF EXISTS `banned_ip`;
CREATE TABLE `banned_ip` (
  `id` int(11) NOT NULL auto_increment,
  `mask` varchar(45) NOT NULL,
  `time_end` timestamp NULL default NULL,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `mask` (`mask`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- gameservers
-- ----------------------------

DROP TABLE IF EXISTS `gameservers`;
CREATE TABLE `gameservers` (
  `id` int(11) NOT NULL auto_increment,
  `mask` varchar(45) NOT NULL,
  `password` varchar(65) NOT NULL,
  PRIMARY KEY  (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ====================================================================
-- GAMESERVER TABLES (Game Data)
-- ====================================================================

-- ----------------------------
-- server_variables
-- ----------------------------

CREATE TABLE IF NOT EXISTS `server_variables` (
  `key` varchar(30) NOT NULL,
  `value` varchar(30) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- players
-- ----------------------------

CREATE TABLE IF NOT EXISTS `players` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `account_id` int(11) NOT NULL,
  `account_name` varchar(50) NOT NULL,
  `exp` bigint(20) NOT NULL default '0',
  `recoverexp` bigint(20) NOT NULL default '0',
  `x` float NOT NULL,
  `y` float NOT NULL,
  `z` float NOT NULL,
  `heading` int(11) NOT NULL,
  `world_id` int(11) NOT NULL,
  `gender` enum('MALE','FEMALE') NOT NULL,
  `race` enum('ASMODIANS','ELYOS') NOT NULL,
  `player_class` enum('WARRIOR','GLADIATOR','TEMPLAR','SCOUT','ASSASSIN','RANGER','MAGE','SORCERER','SPIRIT_MASTER','PRIEST','CLERIC','CHANTER') NOT NULL,
  `creation_date` timestamp NULL default NULL,
  `deletion_date` timestamp NULL default NULL,
  `last_online` timestamp NULL default NULL on update CURRENT_TIMESTAMP,
  `cube_size` tinyint(1) NOT NULL default '0',
  `advenced_stigma_slot_size` TINYINT(1) NOT NULL DEFAULT '0',
  `warehouse_size` tinyint(1) NOT NULL default '0',
  `mailboxLetters` tinyint(4) NOT NULL default '0',
  `bind_point` INT NOT NULL default '0',
  `title_id` int(3) NOT NULL default '-1',
  `online` tinyint(1) NOT NULL default '0',
  `note` text,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `name_unique` (`name`),
  KEY `idx_account_id` (`account_id`),
  FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- player_appearance
-- ----------------------------

CREATE TABLE IF NOT EXISTS `player_appearance` (
  `player_id` int(11) NOT NULL,
  `face` int(11) NOT NULL,
  `hair` int(11) NOT NULL,
  `deco` int(11) NOT NULL,
  `tattoo` int(11) NOT NULL,
  `facial_contour` int(11) NOT NULL,
  `skin_rgb` int(11) NOT NULL,
  `hair_rgb` int(11) NOT NULL,
  `lip_rgb` int(11) NOT NULL,
  `eye_rgb` int(11) NOT NULL,
  `expression` int(11) NOT NULL,
  `face_shape` int(11) NOT NULL,
  `jaw_line` int(11) NOT NULL,
  `forehead` int(11) NOT NULL,
  `eye_height` int(11) NOT NULL,
  `eye_space` int(11) NOT NULL,
  `eye_width` int(11) NOT NULL,
  `eye_size` int(11) NOT NULL,
  `eye_shape` int(11) NOT NULL,
  `eye_angle` int(11) NOT NULL,
  `brow_height` int(11) NOT NULL,
  `brow_angle` int(11) NOT NULL,
  `brow_shape` int(11) NOT NULL,
  `nose` int(11) NOT NULL,
  `nose_bridge` int(11) NOT NULL,
  `nose_width` int(11) NOT NULL,
  `nose_tip` int(11) NOT NULL,
  `cheek` int(11) NOT NULL,
  `lip_height` int(11) NOT NULL,
  `mouth_size` int(11) NOT NULL,
  `lip_size` int(11) NOT NULL,
  `smile` int(11) NOT NULL,
  `lip_shape` int(11) NOT NULL,
  `jaw_height` int(11) NOT NULL,
  `chin_jut` int(11) NOT NULL,
  `ear_shape` int(11) NOT NULL,
  `head_size` int(11) NOT NULL,
  `neck` int(11) NOT NULL,
  `neck_length` int(11) NOT NULL,
  `shoulders` int(11) NOT NULL,
  `shoulder_size` int(11) NOT NULL,
  `torso` int(11) NOT NULL,
  `chest` int(11) NOT NULL,
  `waist` int(11) NOT NULL,
  `hips` int(11) NOT NULL,
  `arm_thickness` int(11) NOT NULL,
  `arm_length` int(11) NOT NULL,
  `hand_size` int(11) NOT NULL,
  `leg_thickness` int(11) NOT NULL,
  `leg_length` int(11) NOT NULL,
  `foot_size` int(11) NOT NULL,
  `facial_rate` int(11) NOT NULL,
  `voice` int(11) NOT NULL,
  `height` float NOT NULL,
  PRIMARY KEY (`player_id`),
  CONSTRAINT `player_id_fk` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- player_macrosses
-- ----------------------------

CREATE TABLE IF NOT EXISTS `player_macrosses` (
  `player_id` int(11) NOT NULL,
  `order` int(3) NOT NULL,
  `macro` text NOT NULL,
  UNIQUE KEY `main` (`player_id`,`order`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- player_titles
-- ----------------------------
CREATE TABLE IF NOT EXISTS `player_titles` (
  `player_id` int(11) NOT NULL,
  `title_id` int(11) NOT NULL,
  `title_expires_time` BIGINT( 20 ) NOT NULL DEFAULT '0',
  `title_date` TIMESTAMP NOT NULL DEFAULT '2010-01-01 00:00:01',
  PRIMARY KEY (`player_id`,`title_id`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- friends
-- ----------------------------

CREATE TABLE IF NOT EXISTS `friends` (
  `player` int(11) NOT NULL,
  `friend` int(11) NOT NULL,
  PRIMARY KEY (`player`,`friend`),
  FOREIGN KEY (`player`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`friend`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- blocks
-- ----------------------------

CREATE TABLE IF NOT EXISTS `blocks` (
  `player` int(11) NOT NULL,
  `blocked_player` int(11) NOT NULL,
  `reason` varchar(100) NOT NULL DEFAULT '',
  PRIMARY KEY (`player`,`blocked_player`),
  FOREIGN KEY (`player`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`blocked_player`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- player_skills
-- ----------------------------

CREATE TABLE IF NOT EXISTS `player_skills` (
  `player_id` int(11) NOT NULL,
  `skillId` int(11) NOT NULL,
  `skillLevel` int(3) NOT NULL default '1',
  PRIMARY KEY  (`player_id`,`skillId`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- inventory
-- ----------------------------

CREATE TABLE IF NOT EXISTS `inventory` (
  `itemUniqueId` int(11) NOT NULL,
  `itemId` int(11) NOT NULL,
  `itemCount` bigint(20) NOT NULL DEFAULT '0',
  `itemColor` int(11) NOT NULL DEFAULT '0',
  `itemOwner` int(11) NOT NULL,
  `isEquiped` TINYINT(1) NOT NULL DEFAULT '0',
  `isSoulBound` TINYINT(1) NOT NULL DEFAULT '0',
  `slot` INT NOT NULL DEFAULT '0',
  `itemLocation` TINYINT(1) DEFAULT '0',
  `enchant` TINYINT(1) DEFAULT '0',
  `itemCreator` varchar(50),
  `itemSkin`  int(11) NOT NULL DEFAULT 0,
  `fusionedItem` INT(11) NOT NULL DEFAULT '0',
  `optionalSocket` INT(1) NOT NULL DEFAULT '0',
  `optionalFusionSocket` INT(1) NOT NULL DEFAULT '0',
  `expireTime` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`itemUniqueId`),
  KEY `item_owner`(`itemOwner`),
  KEY `item_location`(`itemLocation`),
  KEY `is_equiped`(`isEquiped`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- ----------------------------
-- item_stones
-- ----------------------------

CREATE TABLE IF NOT EXISTS `item_stones` (
  `itemUniqueId` int(11) NOT NULL,
  `itemId` int(11) NOT NULL,
  `slot` int(2) NOT NULL,
  `category` int(2) NOT NULL default 0,
  PRIMARY KEY (`itemUniqueId`, `slot`, `category`),
  FOREIGN KEY (`itemUniqueId`) references inventory (`itemUniqueId`) ON DELETE CASCADE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- ----------------------------
-- player_quests
-- ----------------------------

CREATE TABLE IF NOT EXISTS `player_quests` (
`player_id` int(11) NOT NULL,
`quest_id` int(10) unsigned NOT NULL default '0',
`status` varchar(10) NOT NULL default 'NONE',
`quest_vars` int(10) unsigned NOT NULL default '0',
`complete_count` int(3) unsigned NOT NULL default '0',
PRIMARY KEY (`player_id`,`quest_id`),
FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- droplist
-- ----------------------------

CREATE TABLE IF NOT EXISTS `droplist` (
`Id` int(11) NOT NULL AUTO_INCREMENT,
`mobId` int(11) NOT NULL DEFAULT 0,
`itemId` int(11) NOT NULL DEFAULT 0,
`min` int(11) NOT NULL DEFAULT 0,
`max` int(11) NOT NULL DEFAULT 0,
`chance` FLOAT NOT NULL DEFAULT 0,
PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- abyss_rank
-- ----------------------------

CREATE TABLE IF NOT EXISTS `abyss_rank` (
  `player_id` int(11) NOT NULL,
  `daily_ap`  int(11) NOT NULL,
  `weekly_ap` int(11) NOT NULL,
  `ap` int(11) NOT NULL,
  `daily_glory` int(11) NOT NULL DEFAULT '0',
  `weekly_glory` int(11) NOT NULL DEFAULT '0',
  `glory` int(11) NOT NULL DEFAULT '0',
  `rank` int(2) NOT NULL default '1',
  `top_ranking` int(5) NOT NULL DEFAULT '0',
  `old_ranking` int(5) NOT NULL DEFAULT '0',
  `daily_kill` int(5) NOT NULL,
  `weekly_kill`  int(5) NOT NULL,
  `all_kill` int(4) NOT NULL default '0',
  `max_rank` int(2) NOT NULL default '1',
  `last_kill`  int(5) NOT NULL,
  `last_ap`  int(11) NOT NULL,
  `last_glory` int(11) NOT NULL DEFAULT '0',
  `last_update`  decimal(20,0) NOT NULL,
  PRIMARY KEY  (`player_id`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- legions
-- ----------------------------

CREATE TABLE IF NOT EXISTS `legions` (
  `id` int(11) NOT NULL,
  `name` varchar(16) NOT NULL,
  `rank` int(11) NOT NULL default '0',
  `oldrank` int(11) NOT NULL default '0',
  `level` int(1) NOT NULL default '1',
  `contribution_points` int(11) NOT NULL default '0',
  `deputy_permission1` int(4) NOT NULL default '12',
  `deputy_permission2` int(4) NOT NULL default '30',
  `centurion_permission1` int(4) NOT NULL default '8',
  `centurion_permission2` int(4) NOT NULL default '28',
  `legionary_permission1` int(4) NOT NULL default '0',
  `legionary_permission2` int(4) NOT NULL default '24',
  `volunteer_permission1` int(4) NOT NULL default '0',
  `volunteer_permission2` int(4) NOT NULL default '8',
  `disband_time` int(11) NOT NULL default '0',
  PRIMARY KEY  (`id`),
  UNIQUE KEY `name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `legion_announcement_list` (
  `legion_id` int(11) NOT NULL,
  `announcement` varchar(120) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `legion_members` (
  `legion_id` int(11) NOT NULL,
  `player_id` int(11) NOT NULL,
  `nickname` varchar(16) NOT NULL default '',
  `rank` enum( 'BRIGADE_GENERAL', 'CENTURION', 'LEGIONARY' ) NOT NULL DEFAULT 'LEGIONARY',
  `selfintro` varchar(25) default '',
  PRIMARY KEY  (`player_id`),
  KEY `player_id`(`player_id`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `legion_emblems` (
  `legion_id` int(11) NOT NULL,
  `emblem_ver` int(3) NOT NULL default '0',
  `color_r` int(3) NOT NULL default '0',
  `color_g` int(3) NOT NULL default '0',
  `color_b` int(3) NOT NULL default '0',
  `custom` tinyint(1) NOT NULL default '0',
	`emblem_data` longblob,
  PRIMARY KEY  (`legion_id`),
  FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `legion_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `legion_id` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `history_type` enum('CREATE','JOIN','KICK','LEVEL_UP','APPOINTED','EMBLEM_REGISTER','EMBLEM_MODIFIED') NOT NULL,
  `name` varchar(16) NOT NULL,
  PRIMARY KEY  (`id`),
  FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- player_recipes
-- ----------------------------
CREATE TABLE IF NOT EXISTS `player_recipes` (
  `player_id` int(11) NOT NULL,
  `recipe_id` int(11) NOT NULL,
  PRIMARY KEY  (`player_id`,`recipe_id`),
  FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- player_punisments
-- ----------------------------
CREATE TABLE IF NOT EXISTS `player_punishments` (
`player_id` int(11) NOT NULL,
`punishment_status` TINYINT UNSIGNED DEFAULT 0,
`punishment_timer` INT UNSIGNED DEFAULT 0,
PRIMARY KEY (`player_id`),
FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- mail_table
-- ----------------------------
CREATE TABLE IF NOT EXISTS  `mail` (
`mailUniqueId` int(11) NOT NULL,
`mailRecipientId` int(11) NOT NULL,
`senderName` varchar(50) character set utf8 NOT NULL,
`mailTitle` varchar(20) character set utf8 NOT NULL,
`mailMessage` varchar(1000) character set utf8 NOT NULL,
`unread` tinyint(4) NOT NULL default '1',
`attachedItemId` int(11) NOT NULL,
`attachedKinahCount` bigint(20) NOT NULL,
`express` tinyint(4) NOT NULL default '0',
`recievedTime` timestamp NOT NULL default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP,
PRIMARY KEY  (`mailUniqueId`),
INDEX (`mailRecipientId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- player_effects
-- ----------------------------
CREATE TABLE IF NOT EXISTS `player_effects` (
`player_id` int(11) NOT NULL,
`skill_id` int(11) NOT NULL,
`skill_lvl` tinyint NOT NULL,
`current_time`int(11) NOT NULL,
`reuse_delay` BIGINT(13) NOT NULL,
PRIMARY KEY (`player_id`, `skill_id`),
FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- item_cooldowns
-- ----------------------------
CREATE TABLE IF NOT EXISTS `item_cooldowns` (
`player_id` int(11) NOT NULL,
`delay_id` int(11) NOT NULL,
`use_delay` SMALLINT UNSIGNED NOT NULL,
`reuse_time` BIGINT(13) NOT NULL,
PRIMARY KEY (`player_id`, `delay_id`),
FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- broker
-- ----------------------------
CREATE TABLE IF NOT EXISTS `broker` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itemPointer` int(11) NOT NULL DEFAULT '0',
  `itemId` int(11) NOT NULL,
  `itemCount` bigint(20) NOT NULL,
  `seller` varchar(16) NOT NULL,
  `price` bigint(20) NOT NULL DEFAULT '0',
  `brokerRace` enum('ELYOS','ASMODIAN') NOT NULL,
  `expireTime` timestamp NOT NULL DEFAULT '2010-01-01 02:00:00',
  `settleTime` timestamp NOT NULL DEFAULT '2010-01-01 02:00:00',
  `sellerId` int(11) NOT NULL,
  `isSold` tinyint(1) NOT NULL,
  `isSettled` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- bookmark
-- ----------------------------
CREATE TABLE IF NOT EXISTS `bookmark` (
  `id` int(11) NOT NULL auto_increment,
  `name` varchar(50) default NULL,
  `char_id` int(11) NOT NULL,
  `x` float NOT NULL,
  `y` float NOT NULL,
  `z` float NOT NULL,
  `world_id` int(11) NOT NULL,
  PRIMARY KEY  (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `announcements`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `announcements` (
  `id` int(3) NOT NULL AUTO_INCREMENT,
  `announce` text NOT NULL,
  `faction` enum('ALL','ASMODIANS','ELYOS') NOT NULL DEFAULT 'ALL',
  `type` enum('ANNOUNCE','SHOUT','ORANGE','YELLOW','NORMAL') NOT NULL DEFAULT 'ANNOUNCE',
  `delay` int(4) NOT NULL DEFAULT '1800',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- `player_life_stats`
-- ----------------------------

CREATE  TABLE IF NOT EXISTS `player_life_stats` (
  `player_id` INT(11) NOT NULL ,
  `hp` INT(11) NOT NULL DEFAULT 1 ,
  `mp` INT(11) NOT NULL DEFAULT 1 ,
  `fp` INT(11) NOT NULL DEFAULT 1 ,
  PRIMARY KEY (`player_id`) )
ENGINE = MyISAM DEFAULT CHARSET=UTF8;

-- ----------------------------
-- `siege_locations`
-- ----------------------------

CREATE TABLE IF NOT EXISTS `siege_locations` (
  `id` int(11) NOT NULL,
  `race` enum('ELYOS', 'ASMODIANS', 'BALAUR') NOT NULL,
  `legion_id` int (11) NOT NULL,
  PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `petitions`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `petitions` (
  `id` bigint(11) NOT NULL,
  `playerId` int(11) NOT NULL,
  `type` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `addData` varchar(255) default NULL,
  `time` bigint(11) NOT NULL default '0',
  `status` enum('PENDING','IN_PROGRESS','REPLIED') NOT NULL,
  PRIMARY KEY  (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `npc_shouts`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `npc_shouts` (
  `npc_id` int(11) NOT NULL,
  `message_id` int(11) NOT NULL,
  `_interval` int(11) NOT NULL,
  PRIMARY KEY  (`npc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `player_world_bans`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `player_world_bans` (
	`player` int(11) NOT NULL,
	`by` varchar(255) NOT NULL,
	`duration` bigint(11) NOT NULL,
	`date` bigint(11) NOT NULL,
	`reason` varchar(255) NOT NULL,
	PRIMARY KEY (`player`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `instance_time`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `instance_time` (
  `playerId` int(11) DEFAULT NULL,
  `instanceId` int(11) DEFAULT NULL,
  `CheckIn` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `spawns`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `spawns` (
	`spawn_id` int(11) NOT NULL AUTO_INCREMENT,
	`object_id` int(11) NOT NULL,
	`admin_id` int(11) NOT NULL,
	`group_name` varchar(255) DEFAULT NULL,
	`npc_id` int(11) NOT NULL,
	`respawn` tinyint(1) NOT NULL DEFAULT 0,
	`map_id` int(11) NOT NULL,
	`x` float(11) NOT NULL,
	`y` float(11) NOT NULL,
	`z` float(11) NOT NULL,
	`h` tinyint(8) NOT NULL,
	`spawned` tinyint(1) NOT NULL DEFAULT 0,
	`staticid` int(11) NOT NULL DEFAULT 0,
	PRIMARY KEY (`spawn_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `spawn_groups`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `spawn_groups` (
	`admin_id` int(11) NOT NULL,
	`group_name` varchar(255) NOT NULL,
	`spawned` tinyint(1) NOT NULL DEFAULT 0,
	PRIMARY KEY (`admin_id`, `group_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `siege_log`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `siege_log` (
  `log_uuid` bigint(20) NOT NULL auto_increment,
  `legion_name` varchar(255) NOT NULL default '',
  `action` enum('CAPTURE','DEFEND') NOT NULL,
  `tstamp` bigint(20) NOT NULL,
  `siegeloc_id` bigint(20) NOT NULL,
  PRIMARY KEY  (`log_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `player_pets`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `player_pets` (
`idx` int(11) NOT NULL AUTO_INCREMENT,
`player_id` int(11) NOT NULL,
`pet_id` int(11) NOT NULL,
`decoration` int(11) NOT NULL,
`name` varchar(255) NOT NULL,
PRIMARY KEY (`idx`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

-- ----------------------------
-- `player_surveys`
-- ----------------------------
DROP TABLE IF EXISTS `player_surveys`;
CREATE TABLE `player_surveys` (
  `survey_id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `option_id` tinyint(1) NOT NULL,
  PRIMARY KEY (`survey_id`,`player_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- `surveys`
-- ----------------------------
DROP TABLE IF EXISTS `surveys`;
CREATE TABLE `surveys` (
  `survey_id` int(11) NOT NULL AUTO_INCREMENT,
  `owner_id` int(11) NOT NULL,
  `title` varchar(127) NOT NULL,
  `message` varchar(1023) NOT NULL,
  `itemId` int(11) DEFAULT NULL,
  `itemCount` int(11) DEFAULT NULL,
  `player_level_min` tinyint(1) DEFAULT NULL,
  `player_level_max` tinyint(1) DEFAULT NULL,
  `survey_all` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`survey_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- `surveys_option`
-- ----------------------------
DROP TABLE IF EXISTS `surveys_option`;
CREATE TABLE `surveys_option` (
  `survey_id` int(11) NOT NULL AUTO_INCREMENT,
  `option_id` tinyint(1) NOT NULL,
  `option_text` varchar(255) NOT NULL,
  `itemId` int(11) DEFAULT NULL,
  `itemCount` int(11) DEFAULT NULL,
  PRIMARY KEY (`survey_id`,`option_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ----------------------------
-- `player_passkey`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `player_passkey` (
  `account_id` int(11) NOT NULL,
  `passkey` varchar(8) NOT NULL DEFAULT '',
  PRIMARY KEY (`account_id`,`passkey`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `might`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `might` (
  `account_id` int(11) NOT NULL,
  `might` int(11) DEFAULT NULL,
  PRIMARY KEY (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `ladder_player`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `ladder_player` (
  `player_id` int(11) NOT NULL,
  `rating` int(11) default '1000',
  `wins` int(11) default NULL,
  `losses` int(11) default NULL,
  `leaves` int(11) default NULL,
  `rank` int(11) NOT NULL default '-1',
  `last_rank` int(11) NOT NULL default '-1',
  `last_update` timestamp NULL default NULL,
  PRIMARY KEY  (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `bet_system`
-- ----------------------------
CREATE TABLE IF NOT EXISTS `betsystem_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `value` int(11) DEFAULT '-1',
  `name` varchar(45) NOT NULL,
  `groupname` varchar(45) NOT NULL DEFAULT 'GLOBAL',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `betsystem_conditions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `conditionID` int(11) NOT NULL,
  `eventID` int(11) NOT NULL,
  `expectedValue` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8;

-- ----------------------------
-- `shopcategories`
-- ----------------------------

CREATE TABLE IF NOT EXISTS `shopcategories` (
  `id` int(11) NOT NULL auto_increment,
  `name` varchar(30) NOT NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 AUTO_INCREMENT=2 ;

-- ----------------------------
-- `shopitems`
-- ----------------------------

CREATE TABLE IF NOT EXISTS `shopitems` (
  `id` int(11) NOT NULL auto_increment,
  `categoryid` int(11) NOT NULL,
  `itemId` int(11) NOT NULL,
  `count` int(11) NOT NULL,
  `price` int(11) default NULL,
  `name` varchar(50) NOT NULL,
  `type` enum('NORMAL','NEW','HOT') NOT NULL default 'NORMAL',
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 ;

-- ----------------------------
-- `shoplog`
-- ----------------------------

CREATE TABLE IF NOT EXISTS `shoplog` (
  `id` int(11) NOT NULL auto_increment,
  `date` timestamp NULL default NULL,
  `accountId` int(11) NOT NULL,
  `itemId` int(11) NOT NULL,
  `count` int(11) NOT NULL,
  `price` int(11) NOT NULL,
  PRIMARY KEY  (`id`)
) ENGINE=MyISAM  DEFAULT CHARSET=utf8 ;

-- ----------------------------
-- `shop_purchases` (Missing table for CashShopManager)
-- ----------------------------

CREATE TABLE IF NOT EXISTS `shop_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `char_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` bigint(20) NOT NULL DEFAULT '1',
  `gift` tinyint(1) NOT NULL DEFAULT '0',
  `gifter` varchar(50) DEFAULT NULL,
  `added` tinyint(1) NOT NULL DEFAULT '0',
  `purchase_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `char_id` (`char_id`),
  KEY `added` (`added`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- `shop_removals` (Missing table for CashShopManager)
-- ----------------------------

CREATE TABLE IF NOT EXISTS `shop_removals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `itemUniqueId` int(11) NOT NULL,
  `itemOwner` int(11) NOT NULL,
  `amount` int(11) NOT NULL DEFAULT '1',
  `removed` int(11) NOT NULL DEFAULT '0',
  `removal_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `itemUniqueId` (`itemUniqueId`),
  KEY `itemOwner` (`itemOwner`),
  KEY `removed` (`removed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ====================================================================
-- SAMPLE DATA
-- ====================================================================

-- Insert default admin account (username: admin, password: admin)
INSERT INTO `account_data` (`name`, `password`, `activated`, `access_level`)
VALUES ('admin', 'admin', TRUE, 3);

-- Insert a test user account (username: test, password: test)
INSERT INTO `account_data` (`name`, `password`, `activated`, `access_level`)
VALUES ('test', 'test', TRUE, 0);

-- ====================================================================
-- ENABLE FOREIGN KEY CHECKS
-- ====================================================================

SET FOREIGN_KEY_CHECKS = 1;

-- ====================================================================
-- SETUP COMPLETE - ALL TABLES INCLUDED + MISSING TABLES ADDED!
-- ====================================================================
-- Complete Aion database with ALL tables from both servers + missing tables!
--
-- LoginServer Tables: ✅ account_data, account_time, banned_ip, gameservers
-- GameServer Tables: ✅ ALL 50+ tables including:
-- - players, player_appearance, player_skills, player_quests
-- - inventory, item_stones, item_cooldowns
-- - legions, legion_members, legion_emblems, legion_history
-- - abyss_rank, broker, mail, friends, blocks
-- - droplist, spawns, siege_locations, petitions
-- - surveys, player_pets, announcements, bookmarks
-- - shopcategories, shopitems, shoplog
-- - shop_purchases, shop_removals (ADDED - fixes CashShopManager errors!)
-- - And many more!
--
-- Default accounts created:
-- - admin/admin (access level 3 - Administrator)
-- - test/test (access level 0 - Regular user)
--
-- ✅ FIXES: Added missing shop_purchases and shop_removals tables
-- ✅ NO MORE: "Table doesn't exist" errors for CashShopManager
-- ✅ READY: Complete database for import!
-- ====================================================================
