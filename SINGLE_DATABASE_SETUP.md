# Aion Single Database Setup - EASY IMPORT

## 🎯 Quick Solution
I've created a **single combined SQL file** that includes both LoginServer and GameServer tables in one database. This solves your import error!

## 📁 Files Created
- `aion_complete_database.sql` - **Complete database with all tables**
- `SINGLE_DATABASE_SETUP.md` - This instruction file

## 🚀 How to Import (3 Simple Steps)

### Step 1: Create Database in phpMyAdmin
1. Open XAMPP Control Panel → Start Apache and MySQL
2. Go to `http://localhost/phpmyadmin`
3. Click "New" to create a database
4. Name it: `aionx_complete`
5. Click "Create"

### Step 2: Import the Combined SQL File
1. Select the `aionx_complete` database
2. Click "Import" tab
3. Choose file: `aion_complete_database.sql`
4. Click "Go"
5. ✅ **Success!** All tables imported in one go!

### Step 3: Update Configuration Files

#### LoginServer Config
Edit: `LoginServer/config/database.properties`
```properties
database.driver=com.mysql.jdbc.Driver
database.url=**********************************************************************************
database.user=root
database.password=
```

#### GameServer Config  
Edit: `GameServer/config/database.properties`
```properties
database.driver=com.mysql.jdbc.Driver
database.url=**********************************************************************************
database.user=root
database.password=
```

## 🎮 Ready to Play!

### Default Accounts Created
- **Username:** `admin` **Password:** `admin` (Administrator)
- **Username:** `test` **Password:** `test` (Regular user)

### What's Included
✅ All LoginServer tables (accounts, bans, etc.)  
✅ All GameServer tables (players, inventory, quests, etc.)  
✅ Proper foreign key relationships  
✅ Sample accounts ready to use  
✅ No more import errors!  

## 🔧 Next Steps
1. Start LoginServer
2. Start GameServer  
3. Connect with test account
4. Create characters and enjoy!

## 💡 Why This Works
The original error happened because:
- GameServer tables reference LoginServer accounts
- You were importing only GameServer tables
- Missing account references caused the foreign key error

**Solution:** Combined both databases into one file with proper table order and relationships!

## 🆘 Troubleshooting
If you still get errors:
1. Make sure XAMPP MySQL is running
2. Check database name matches config files exactly: `aionx_complete`
3. Verify root user has no password (default XAMPP setup)
4. Try importing with foreign key checks disabled (already handled in the SQL file)
